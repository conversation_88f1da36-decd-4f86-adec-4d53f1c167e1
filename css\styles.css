/* <PERSON><PERSON><PERSON> <PERSON>, <PERSON>, <PERSON> Design */
body {
    font-family: 'Inter', sans-serif;
    background-color: #000000;
    color: #ffffff;
    line-height: 1.6;
}

.font-lora {
    font-family: 'Lora', serif;
}

/* Modal Animations */
.modal {
    display: none;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Chart Container */
.chart-container {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    height: 350px;
    max-height: 400px;
}

@media (min-width: 768px) {
    .chart-container {
        height: 400px;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #606060;
}

/* Gallery Cards */
.gallery-card {
    background: #1a1a1a;
    border: 1px solid #333333;
    transition: all 0.3s ease;
    cursor: pointer;
}

.gallery-card:hover {
    background: #262626;
    border-color: #666666;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

/* Chart Cards */
.chart-card {
    background: #1a1a1a;
    border: 1px solid #333333;
}

/* Header */
.header-bg {
    background: #000000;
    border-bottom: 1px solid #333333;
}

/* Modal Styling */
.modal-backdrop {
    background: rgba(0, 0, 0, 0.9);
}

.modal-content-bg {
    background: #1a1a1a;
    border: 1px solid #333333;
}

.modal-divider {
    border-color: #333333;
}

.modal-sidebar {
    background: #0d0d0d;
    border-left: 1px solid #333333;
}

/* Text Colors */
.text-primary {
    color: #ffffff;
}

.text-secondary {
    color: #a0a0a0;
}

.text-muted {
    color: #666666;
}

/* Buttons */
.btn-close {
    color: #a0a0a0;
    transition: color 0.2s ease;
}

.btn-close:hover {
    color: #ffffff;
}

/* Scene Details Styling */
.scene-detail-item {
    background: #262626;
    border: 1px solid #404040;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.scene-detail-item:hover {
    background: #2d2d2d;
    border-color: #505050;
}

.scene-detail-label {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.scene-detail-value {
    color: #a0a0a0;
    line-height: 1.5;
}

/* Additional Minimalistic Enhancements */
.container {
    max-width: 1200px;
}

/* Smooth transitions for all interactive elements */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Focus states for accessibility */
.gallery-card:focus,
.btn-close:focus {
    outline: 2px solid #ffffff;
    outline-offset: 2px;
}

/* Typography improvements */
h1, h2, h3, h4 {
    letter-spacing: -0.025em;
}

/* Subtle text selection styling */
::selection {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

/* Advanced Search Styling */
.search-container {
    max-width: 800px;
}

.search-input-wrapper {
    position: relative;
}

.search-input {
    background: #1a1a1a;
    border-color: #333333;
    color: #ffffff;
    font-size: 1rem;
    padding: 1rem 3rem 1rem 1.5rem;
}

.search-input::placeholder {
    color: #666666;
}

.search-input:focus {
    background: #262626;
    border-color: #ffffff;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.search-clear-btn {
    color: #666666;
    cursor: pointer;
    font-weight: 300;
    line-height: 1;
    padding: 0.5rem;
}

.search-clear-btn.visible {
    opacity: 1;
}

.search-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.filter-btn {
    background: #262626;
    border: 1px solid #404040;
    color: #a0a0a0;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-btn:hover {
    background: #333333;
    border-color: #606060;
    color: #ffffff;
}

.filter-btn.active {
    background: #ffffff;
    border-color: #ffffff;
    color: #000000;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
}

.search-results-info {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Search Highlighting */
.search-highlight {
    background: rgba(255, 255, 255, 0.3);
    color: #ffffff;
    padding: 0.1rem 0.2rem;
    border-radius: 0.25rem;
    font-weight: 600;
}

/* No Results State */
.no-results {
    text-align: center;
    padding: 3rem 1rem;
    color: #666666;
}

.no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #a0a0a0;
}

.no-results p {
    font-size: 1rem;
    line-height: 1.6;
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #1a1a1a;
    border: 1px solid #333333;
    border-top: none;
    border-radius: 0 0 0.75rem 0.75rem;
    max-height: 200px;
    overflow-y: auto;
    z-index: 10;
    display: none;
}

.search-suggestion {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #2a2a2a;
    color: #a0a0a0;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.search-suggestion:hover,
.search-suggestion.highlighted {
    background: #262626;
    color: #ffffff;
}

.search-suggestion:last-child {
    border-bottom: none;
}

.search-suggestion-category {
    font-size: 0.75rem;
    color: #666666;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Responsive search */
@media (max-width: 768px) {
    .search-filters {
        justify-content: center;
    }

    .filter-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    .search-input {
        font-size: 1rem;
        padding: 0.875rem 2.5rem 0.875rem 1rem;
    }
}
