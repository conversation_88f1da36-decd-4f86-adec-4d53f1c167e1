/* <PERSON><PERSON><PERSON> <PERSON>, <PERSON>, <PERSON> Design */
body {
    font-family: 'Inter', sans-serif;
    background-color: #000000;
    color: #ffffff;
    line-height: 1.6;
}

.font-lora {
    font-family: 'Lora', serif;
}

/* Modal Animations */
.modal {
    display: none;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Chart Container */
.chart-container {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    height: 350px;
    max-height: 400px;
}

@media (min-width: 768px) {
    .chart-container {
        height: 400px;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #606060;
}

/* Gallery Cards */
.gallery-card {
    background: #1a1a1a;
    border: 1px solid #333333;
    transition: all 0.3s ease;
    cursor: pointer;
}

.gallery-card:hover {
    background: #262626;
    border-color: #666666;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

/* Chart Cards */
.chart-card {
    background: #1a1a1a;
    border: 1px solid #333333;
}

/* Header */
.header-bg {
    background: #000000;
    border-bottom: 1px solid #333333;
}

/* Modal Styling */
.modal-backdrop {
    background: rgba(0, 0, 0, 0.9);
}

.modal-content-bg {
    background: #1a1a1a;
    border: 1px solid #333333;
}

.modal-divider {
    border-color: #333333;
}

.modal-sidebar {
    background: #0d0d0d;
    border-left: 1px solid #333333;
}

/* Text Colors */
.text-primary {
    color: #ffffff;
}

.text-secondary {
    color: #a0a0a0;
}

.text-muted {
    color: #666666;
}

/* Buttons */
.btn-close {
    color: #a0a0a0;
    transition: color 0.2s ease;
}

.btn-close:hover {
    color: #ffffff;
}

/* Scene Details Styling */
.scene-detail-item {
    background: #262626;
    border: 1px solid #404040;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.scene-detail-item:hover {
    background: #2d2d2d;
    border-color: #505050;
}

.scene-detail-label {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.scene-detail-value {
    color: #a0a0a0;
    line-height: 1.5;
}

/* Additional Minimalistic Enhancements */
.container {
    max-width: 1200px;
}

/* Smooth transitions for all interactive elements */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Focus states for accessibility */
.gallery-card:focus,
.btn-close:focus {
    outline: 2px solid #ffffff;
    outline-offset: 2px;
}

/* Typography improvements */
h1, h2, h3, h4 {
    letter-spacing: -0.025em;
}

/* Subtle text selection styling */
::selection {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}
