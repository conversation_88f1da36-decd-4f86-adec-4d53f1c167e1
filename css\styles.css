body {
    font-family: 'Inter', sans-serif;
    background-color: #f1f5f9; /* slate-100 */
}
.font-lora {
    font-family: 'Lora', serif;
}
.modal {
    display: none;
    animation: fadeIn 0.3s ease-out;
}
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
.modal-content {
    animation: slideInUp 0.4s ease-out;
}
@keyframes slideInUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
.chart-container {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    height: 350px;
    max-height: 400px;
}
@media (min-width: 768px) {
    .chart-container {
        height: 400px;
    }
}
::-webkit-scrollbar {
    width: 8px;
}
::-webkit-scrollbar-track {
    background: #e2e8f0; /* slate-200 */
}
::-webkit-scrollbar-thumb {
    background: #94a3b8; /* slate-400 */
    border-radius: 10px;
}
::-webkit-scrollbar-thumb:hover {
    background: #64748b; /* slate-500 */
}
