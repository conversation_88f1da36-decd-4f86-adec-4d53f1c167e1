// Test script to validate scene data
const fs = require('fs');

// Read the scenes.js file
const sceneDataContent = fs.readFileSync('./data/scenes.js', 'utf8');

// Execute the content to define sceneData
let sceneData;
eval(sceneDataContent);

console.log('Data loaded successfully. Found', sceneData.length, 'scenes');

// Check for any remaining "Not specified" clothing entries
let issuesFound = 0;
sceneData.forEach((scene, i) => {
    if (!scene.details.masculine.clothing || scene.details.masculine.clothing === 'Not specified') {
        console.log('Issue with scene', i+1, ':', scene.title);
        issuesFound++;
    }
});

if (issuesFound === 0) {
    console.log('✅ All scenes have proper masculine clothing specified');
} else {
    console.log('❌ Found', issuesFound, 'scenes with missing clothing data');
}

// Validate required properties
let validationErrors = 0;
sceneData.forEach((scene, i) => {
    const requiredProps = ['title', 'effeminateAge', 'masculineAge', 'setting', 'emotion', 'details', 'fullText'];
    requiredProps.forEach(prop => {
        if (!scene[prop]) {
            console.log(`❌ Scene ${i+1} missing property: ${prop}`);
            validationErrors++;
        }
    });
});

if (validationErrors === 0) {
    console.log('✅ All scenes have required properties');
} else {
    console.log('❌ Found', validationErrors, 'validation errors');
}

console.log('Data validation complete!');
