document.addEventListener('DOMContentLoaded', () => {
    // Error handling for DOM elements
    const galleryGrid = document.getElementById('gallery-grid');
    const modal = document.getElementById('scene-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalText = document.getElementById('modal-text');
    const modalDetails = document.getElementById('modal-details');
    const closeModalBtn = document.getElementById('close-modal-btn');

    // Search elements
    const searchInput = document.getElementById('search-input');
    const clearSearchBtn = document.getElementById('clear-search');
    const searchResultsInfo = document.getElementById('search-results-info');
    const filterBtns = document.querySelectorAll('.filter-btn');

    // Check if all required DOM elements exist
    if (!galleryGrid || !modal || !modalTitle || !modalText || !modalDetails || !closeModalBtn) {
        console.error('Required DOM elements not found');
        return;
    }

    // Check if scene data exists
    if (typeof sceneData === 'undefined' || !Array.isArray(sceneData)) {
        console.error('Scene data not found or invalid');
        return;
    }

    const allData = sceneData;

    // Search state
    let currentFilter = 'all';
    let currentSearchTerm = '';
    let filteredScenes = [...allData];

// Function to render gallery cards
const renderGallery = (scenes, searchTerm = '') => {
    galleryGrid.innerHTML = '';

    if (scenes.length === 0) {
        galleryGrid.innerHTML = `
            <div class="no-results col-span-full">
                <h3>No scenes found</h3>
                <p>Try adjusting your search terms or filters to find more scenes.</p>
            </div>
        `;
        return;
    }

    scenes.forEach((scene) => {
        const card = document.createElement('div');
        card.className = 'gallery-card rounded-xl p-6 flex flex-col justify-between min-h-[140px]';

        // Highlight search terms in the content
        const highlightText = (text, term) => {
            if (!term) return text;
            const regex = new RegExp(`(${term})`, 'gi');
            return text.replace(regex, '<span class="search-highlight">$1</span>');
        };

        card.innerHTML = `
            <div>
                <h3 class="text-lg font-bold font-lora text-primary mb-3">${highlightText(scene.title, searchTerm)}</h3>
                <div class="flex items-center text-sm text-secondary mb-2">
                    <span class="mr-2">•</span>
                    <span>${highlightText(scene.setting, searchTerm)}</span>
                </div>
                <div class="flex items-center text-sm text-muted">
                    <span class="mr-2">•</span>
                    <span>${highlightText(scene.emotion, searchTerm)}</span>
                </div>
            </div>
        `;
        card.addEventListener('click', () => openModal(scene));
        galleryGrid.appendChild(card);
    });
};

// Search functionality
const searchScenes = (searchTerm, filter) => {
    const term = searchTerm.toLowerCase().trim();

    if (!term) {
        filteredScenes = [...allData];
        updateSearchResults(filteredScenes.length, allData.length, '');
        return filteredScenes;
    }

    filteredScenes = allData.filter(scene => {
        const searchableContent = getSearchableContent(scene, filter);
        return searchableContent.some(content =>
            content.toLowerCase().includes(term)
        );
    });

    updateSearchResults(filteredScenes.length, allData.length, term);
    return filteredScenes;
};

const getSearchableContent = (scene, filter) => {
    const content = [];

    // Always include title for 'all' filter
    if (filter === 'all') {
        content.push(scene.title);
    }

    if (filter === 'all' || filter === 'setting') {
        content.push(scene.setting);
    }

    if (filter === 'all' || filter === 'emotion') {
        content.push(scene.emotion);
    }

    if (filter === 'all' || filter === 'character') {
        content.push(scene.details.effeminate.appearance);
        content.push(scene.details.effeminate.hair);
        content.push(scene.details.effeminate.clothing);
        content.push(scene.details.masculine.appearance);
        content.push(scene.details.masculine.hair);
        content.push(scene.details.masculine.clothing);
    }

    if (filter === 'all' || filter === 'atmosphere') {
        content.push(scene.details.atmosphere.lighting);
        content.push(scene.details.atmosphere.scent);
        content.push(scene.details.atmosphere.sound);
    }

    if (filter === 'all' && scene.fullText) {
        content.push(scene.fullText);
    }

    return content.filter(item => item && item !== 'Not specified');
};

const updateSearchResults = (found, total, term) => {
    if (!term) {
        searchResultsInfo.textContent = `Showing all ${total} scenes`;
    } else {
        searchResultsInfo.textContent = `Found ${found} of ${total} scenes matching "${term}"`;
    }
};

// Initial render
renderGallery(filteredScenes);
updateSearchResults(filteredScenes.length, allData.length, '');

const openModal = (scene) => {
    modalTitle.textContent = scene.title;
    modalText.innerHTML = `<p>${scene.fullText.replace(/\n/g, '</p><p>')}</p>`;
    
    const { effeminate, masculine, atmosphere } = scene.details;
    modalDetails.innerHTML = `
        <div class="scene-detail-item">
            <h4 class="scene-detail-label text-lg mb-3">Effeminate Male (Age: ${scene.effeminateAge})</h4>
            <div class="space-y-2">
                <div><span class="scene-detail-label">Appearance:</span> <span class="scene-detail-value">${effeminate.appearance}</span></div>
                <div><span class="scene-detail-label">Hair:</span> <span class="scene-detail-value">${effeminate.hair}</span></div>
                <div><span class="scene-detail-label">Clothing:</span> <span class="scene-detail-value">${effeminate.clothing}</span></div>
            </div>
        </div>
        <div class="scene-detail-item">
            <h4 class="scene-detail-label text-lg mb-3">Masculine Male (Age: ${scene.masculineAge})</h4>
            <div class="space-y-2">
                <div><span class="scene-detail-label">Appearance:</span> <span class="scene-detail-value">${masculine.appearance}</span></div>
                <div><span class="scene-detail-label">Hair:</span> <span class="scene-detail-value">${masculine.hair}</span></div>
                <div><span class="scene-detail-label">Clothing:</span> <span class="scene-detail-value">${masculine.clothing}</span></div>
            </div>
        </div>
        <div class="scene-detail-item">
            <h4 class="scene-detail-label text-lg mb-3">Atmosphere</h4>
            <div class="space-y-2">
                <div><span class="scene-detail-label">Lighting:</span> <span class="scene-detail-value">${atmosphere.lighting}</span></div>
                <div><span class="scene-detail-label">Scent:</span> <span class="scene-detail-value">${atmosphere.scent}</span></div>
                <div><span class="scene-detail-label">Sound:</span> <span class="scene-detail-value">${atmosphere.sound}</span></div>
            </div>
        </div>
    `;
    modal.style.display = 'flex';
};

closeModalBtn.addEventListener('click', () => {
    modal.style.display = 'none';
});

window.addEventListener('click', (event) => {
    if (event.target === modal) {
        modal.style.display = 'none';
    }
});

const renderCharts = () => {
    try {
        // Validate chart container elements exist
        const ageChartElement = document.getElementById('ageChart');
        const settingChartElement = document.getElementById('settingChart');
        const emotionChartElement = document.getElementById('emotionChart');

        if (!ageChartElement || !settingChartElement || !emotionChartElement) {
            console.error('Chart container elements not found');
            return;
        }

        const effeminateAges = allData.map(s => s.effeminateAge);
        const masculineAges = allData.map(s => s.masculineAge);

        const settingCounts = allData.reduce((acc, s) => {
            acc[s.setting] = (acc[s.setting] || 0) + 1;
            return acc;
        }, {});

        const emotionCounts = allData.reduce((acc, s) => {
            acc[s.emotion] = (acc[s.emotion] || 0) + 1;
            return acc;
        }, {});

    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: {
                    font: {
                        family: "'Inter', sans-serif"
                    },
                    color: '#ffffff'
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    font: {
                        family: "'Inter', sans-serif"
                    },
                    color: '#a0a0a0'
                },
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            },
            x: {
                ticks: {
                    font: {
                        family: "'Inter', sans-serif"
                    },
                    color: '#a0a0a0'
                },
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        }
    };

        new Chart(ageChartElement, {
        type: 'bar',
        data: {
            labels: allData.map(s => s.title.substring(0, 15) + (s.title.length > 15 ? '...' : '')),
            datasets: [
                {
                    label: 'Effeminate Male Age',
                    data: effeminateAges,
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderColor: 'rgba(255, 255, 255, 1)',
                    borderWidth: 2
                },
                {
                    label: 'Masculine Male Age',
                    data: masculineAges,
                    backgroundColor: 'rgba(160, 160, 160, 0.8)',
                    borderColor: 'rgba(160, 160, 160, 1)',
                    borderWidth: 2
                }
            ]
        },
        options: chartOptions
    });

        new Chart(settingChartElement, {
        type: 'doughnut',
        data: {
            labels: Object.keys(settingCounts),
            datasets: [{
                label: 'Settings',
                data: Object.values(settingCounts),
                backgroundColor: [
                    'rgba(255, 255, 255, 0.9)',
                    'rgba(200, 200, 200, 0.9)',
                    'rgba(160, 160, 160, 0.9)',
                    'rgba(120, 120, 120, 0.9)',
                    'rgba(80, 80, 80, 0.9)',
                    'rgba(100, 116, 139, 0.7)', // slate-500
                    'rgba(139, 92, 246, 0.7)',  // violet-400
                    'rgba(239, 68, 68, 0.7)',  // red-500
                ],
            }]
        },
        options: { ...chartOptions, scales: {} }
    });

        new Chart(emotionChartElement, {
            type: 'radar',
            data: {
                labels: Object.keys(emotionCounts),
                datasets: [{
                    label: 'Emotional Tone Frequency',
                    data: Object.values(emotionCounts),
                    fill: true,
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    borderColor: 'rgb(255, 255, 255)',
                    pointBackgroundColor: 'rgb(255, 255, 255)',
                    pointBorderColor: '#000',
                    pointHoverBackgroundColor: '#000',
                    pointHoverBorderColor: 'rgb(255, 255, 255)'
                }]
            },
            options: { ...chartOptions, scales: {
                r: {
                    angleLines: { color: 'rgba(255, 255, 255, 0.2)' },
                    grid: { color: 'rgba(255, 255, 255, 0.2)' },
                    pointLabels: { font: { size: 14, family: "'Inter', sans-serif" }, color: '#a0a0a0' },
                    ticks: { backdropColor: 'transparent', color: '#a0a0a0' }
                }
            }}
        });
    } catch (error) {
        console.error('Error rendering charts:', error);
    }
};
    renderCharts();

    // Search event listeners
    if (searchInput && clearSearchBtn && filterBtns.length > 0) {
        // Search input event
        searchInput.addEventListener('input', (e) => {
            currentSearchTerm = e.target.value;
            const results = searchScenes(currentSearchTerm, currentFilter);
            renderGallery(results, currentSearchTerm);

            // Show/hide clear button
            if (currentSearchTerm) {
                clearSearchBtn.classList.add('visible');
            } else {
                clearSearchBtn.classList.remove('visible');
            }
        });

        // Clear search button
        clearSearchBtn.addEventListener('click', () => {
            searchInput.value = '';
            currentSearchTerm = '';
            clearSearchBtn.classList.remove('visible');
            const results = searchScenes('', currentFilter);
            renderGallery(results);
        });

        // Filter buttons
        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // Update active filter
                filterBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                currentFilter = btn.dataset.filter;

                // Re-run search with new filter
                const results = searchScenes(currentSearchTerm, currentFilter);
                renderGallery(results, currentSearchTerm);
            });
        });

        // Enter key to focus search
        document.addEventListener('keydown', (e) => {
            if (e.key === '/' && e.target !== searchInput) {
                e.preventDefault();
                searchInput.focus();
            }
            if (e.key === 'Escape' && e.target === searchInput) {
                searchInput.blur();
            }
        });
    }
});