document.addEventListener('DOMContentLoaded', () => {
    // Error handling for DOM elements
    const galleryGrid = document.getElementById('gallery-grid');
    const modal = document.getElementById('scene-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalText = document.getElementById('modal-text');
    const modalDetails = document.getElementById('modal-details');
    const closeModalBtn = document.getElementById('close-modal-btn');

    // Check if all required DOM elements exist
    if (!galleryGrid || !modal || !modalTitle || !modalText || !modalDetails || !closeModalBtn) {
        console.error('Required DOM elements not found');
        return;
    }

    // Check if scene data exists
    if (typeof sceneData === 'undefined' || !Array.isArray(sceneData)) {
        console.error('Scene data not found or invalid');
        return;
    }

    const allData = sceneData;

allData.forEach((scene, index) => {
    const card = document.createElement('div');
    card.className = 'bg-white rounded-lg shadow-md p-5 cursor-pointer hover:shadow-xl hover:-translate-y-1 transition-all duration-300 flex flex-col justify-between';
    card.innerHTML = `
        <div>
            <h3 class="text-lg font-bold font-lora text-slate-800 mb-2">${scene.title}</h3>
            <div class="flex items-center text-sm text-slate-500 mb-3">
                <span class="mr-1">📍</span>
                <span>${scene.setting}</span>
            </div>
        </div>
        <div class="text-right text-amber-600 font-semibold">${scene.emotion}</div>
    `;
    card.addEventListener('click', () => openModal(scene));
    galleryGrid.appendChild(card);
});

const openModal = (scene) => {
    modalTitle.textContent = scene.title;
    modalText.innerHTML = `<p>${scene.fullText.replace(/\n/g, '</p><p>')}</p>`;
    
    const { effeminate, masculine, atmosphere } = scene.details;
    modalDetails.innerHTML = `
        <div class="bg-white p-4 rounded-md border border-slate-200">
            <h4 class="font-bold text-lg mb-2 text-rose-800">Effeminate Male (Age: ${scene.effeminateAge})</h4>
            <ul class="list-none space-y-2 text-sm text-slate-600">
                <li><strong>Appearance:</strong> ${effeminate.appearance}</li>
                <li><strong>Hair:</strong> ${effeminate.hair}</li>
                <li><strong>Clothing:</strong> ${effeminate.clothing}</li>
            </ul>
        </div>
        <div class="bg-white p-4 rounded-md border border-slate-200">
            <h4 class="font-bold text-lg mb-2 text-sky-800">Masculine Male (Age: ${scene.masculineAge})</h4>
            <ul class="list-none space-y-2 text-sm text-slate-600">
                <li><strong>Appearance:</strong> ${masculine.appearance}</li>
                <li><strong>Hair:</strong> ${masculine.hair}</li>
                <li><strong>Clothing:</strong> ${masculine.clothing}</li>
            </ul>
        </div>
            <div class="bg-white p-4 rounded-md border border-slate-200">
            <h4 class="font-bold text-lg mb-2 text-amber-800">Atmosphere</h4>
            <ul class="list-none space-y-2 text-sm text-slate-600">
                <li><strong>Lighting:</strong> ${atmosphere.lighting}</li>
                <li><strong>Scent:</strong> ${atmosphere.scent}</li>
                <li><strong>Sound:</strong> ${atmosphere.sound}</li>
            </ul>
        </div>
    `;
    modal.style.display = 'flex';
};

closeModalBtn.addEventListener('click', () => {
    modal.style.display = 'none';
});

window.addEventListener('click', (event) => {
    if (event.target === modal) {
        modal.style.display = 'none';
    }
});

const renderCharts = () => {
    const effeminateAges = allData.map(s => s.effeminateAge);
    const masculineAges = allData.map(s => s.masculineAge);
    
    const settingCounts = allData.reduce((acc, s) => {
        acc[s.setting] = (acc[s.setting] || 0) + 1;
        return acc;
    }, {});

    const emotionCounts = allData.reduce((acc, s) => {
        acc[s.emotion] = (acc[s.emotion] || 0) + 1;
        return acc;
    }, {});

    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: {
                    font: {
                        family: "'Inter', sans-serif"
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    font: {
                        family: "'Inter', sans-serif"
                    }
                }
            },
            x: {
                ticks: {
                    font: {
                        family: "'Inter', sans-serif"
                    }
                }
            }
        }
    };

    new Chart(document.getElementById('ageChart'), {
        type: 'bar',
        data: {
            labels: allData.map(s => s.title.substring(0, 15) + (s.title.length > 15 ? '...' : '')),
            datasets: [
                {
                    label: 'Effeminate Male Age',
                    data: effeminateAges,
                    backgroundColor: 'rgba(225, 29, 72, 0.6)', // rose-600
                    borderColor: 'rgba(225, 29, 72, 1)',
                    borderWidth: 1
                },
                {
                    label: 'Masculine Male Age',
                    data: masculineAges,
                    backgroundColor: 'rgba(7, 89, 133, 0.6)', // sky-800
                    borderColor: 'rgba(7, 89, 133, 1)',
                    borderWidth: 1
                }
            ]
        },
        options: chartOptions
    });

    new Chart(document.getElementById('settingChart'), {
        type: 'doughnut',
        data: {
            labels: Object.keys(settingCounts),
            datasets: [{
                label: 'Settings',
                data: Object.values(settingCounts),
                backgroundColor: [
                    'rgba(251, 146, 60, 0.7)', // amber-400
                    'rgba(124, 58, 237, 0.7)', // violet-600
                    'rgba(22, 163, 74, 0.7)',  // green-600
                    'rgba(219, 39, 119, 0.7)', // pink-600
                    'rgba(2, 132, 199, 0.7)',  // sky-600
                    'rgba(100, 116, 139, 0.7)', // slate-500
                    'rgba(139, 92, 246, 0.7)',  // violet-400
                    'rgba(239, 68, 68, 0.7)',  // red-500
                ],
            }]
        },
        options: { ...chartOptions, scales: {} }
    });

    new Chart(document.getElementById('emotionChart'), {
        type: 'radar',
        data: {
            labels: Object.keys(emotionCounts),
            datasets: [{
                label: 'Emotional Tone Frequency',
                data: Object.values(emotionCounts),
                fill: true,
                backgroundColor: 'rgba(217, 119, 6, 0.2)', // amber-600
                borderColor: 'rgb(217, 119, 6)',
                pointBackgroundColor: 'rgb(217, 119, 6)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgb(217, 119, 6)'
            }]
        },
        options: { ...chartOptions, scales: {
            r: {
                angleLines: { color: 'rgba(0, 0, 0, 0.1)' },
                grid: { color: 'rgba(0, 0, 0, 0.1)' },
                pointLabels: { font: { size: 14, family: "'Inter', sans-serif" } },
                ticks: { backdropColor: 'transparent', color: '#475569' }
            }
        }}
    });
};
    renderCharts();
});