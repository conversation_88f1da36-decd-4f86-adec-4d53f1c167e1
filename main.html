<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whispers of Intimacy: An Interactive Exploration</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="data/scenes.js"></script>
    <script src="js/script.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Lora:ital,wght@0,400;0,600;1,400&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <!-- Chosen Palette: Warm Neutrals (<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>) -->
    <!-- Application Structure Plan: The SPA is designed as an interactive gallery and analysis dashboard. The main view is a grid of cards, each representing a scene, for a quick, visual overview. Clicking a card opens a full-screen modal, which prevents disorientation by keeping the user on the same page. This modal presents the full text alongside a 'deconstructed' view that breaks down complex descriptions into digestible categories (characters, atmosphere). This structure facilitates both deep reading of individual scenes and easy comparison between them. Below the gallery, an analysis section with charts provides a quantitative overview, synthesizing patterns (like character ages and common settings) from across all the qualitative data, offering a macro-level understanding that complements the micro-level detail in the modals. This layered approach—from gallery overview to modal deep-dive to analytical summary—was chosen to guide the user through a comprehensive and engaging exploration of the source material. -->
    <!-- Visualization & Content Choices: 
        - Report Info: Collection of Scenes -> Goal: Organize/Overview -> Viz: Interactive Cards -> Interaction: Click to open modal -> Justification: Provides a scannable, non-linear entry point to the rich text. -> Method: HTML/Tailwind/JS.
        - Report Info: Full Text -> Goal: Inform -> Viz: Text Block in Modal -> Interaction: Read/Scroll -> Justification: Presents the core content in a focused, uncluttered view. -> Method: HTML/Tailwind.
        - Report Info: Character/Setting Details -> Goal: Inform/Compare -> Viz: Structured Lists with Icons -> Interaction: View in Modal -> Justification: Deconstructs dense prose into scannable key points for quick understanding and comparison. -> Method: HTML/Tailwind.
        - Report Info: Character Ages -> Goal: Analyze -> Viz: Bar Chart -> Interaction: Hover for Tooltip -> Justification: Quantifies a recurring detail, revealing demographic patterns. -> Library: Chart.js/Canvas.
        - Report Info: Scene Settings -> Goal: Analyze -> Viz: Doughnut Chart -> Interaction: Hover for Tooltip -> Justification: Visualizes the frequency of thematic environments. -> Library: Chart.js/Canvas.
        - Report Info: Emotional Tones -> Goal: Analyze -> Viz: Radar Chart -> Interaction: Hover for Tooltip -> Justification: Maps the abstract emotional landscape of the collection for thematic analysis. -> Library: Chart.js/Canvas.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
</head>
<body class="text-slate-800">

    <header class="bg-white shadow-md sticky top-0 z-40">
        <div class="container mx-auto px-6 py-4">
            <h1 class="text-3xl font-bold text-slate-900 font-lora">Whispers of Intimacy</h1>
            <p class="text-slate-600">An Interactive Exploration of Descriptive Scenes</p>
        </div>
    </header>

    <main class="container mx-auto px-6 py-8">
        <section id="gallery-section" class="mb-16">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-slate-900 mb-2 font-lora">Scene Gallery</h2>
                <p class="text-slate-600">This gallery presents a collection of intimate, descriptive scenes. Each card represents a unique vignette, capturing a moment of connection. Click on any card to open a detailed view, which includes the full text of the scene and a breakdown of its key characters and atmospheric elements. This structure allows you to explore the nuances of each story at your own pace.</p>
            </div>
            <div id="gallery-grid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            </div>
        </section>

        <section id="analysis-section">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-slate-900 mb-2 font-lora">Thematic Analysis</h2>
                <p class="text-slate-600">The following visualizations provide a high-level analysis of the entire collection of scenes. These charts synthesize recurring data points and themes, such as character ages, common settings, and emotional tones, to reveal underlying patterns and offer a broader perspective on the narratives. This section helps in understanding the collection not just as individual stories, but as a cohesive whole with distinct characteristics.</p>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
                <div class="bg-white p-6 rounded-lg shadow-md w-full">
                    <h3 class="text-xl font-bold mb-4 text-center font-lora">Character Age Distribution</h3>
                    <div class="chart-container">
                        <canvas id="ageChart"></canvas>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md w-full">
                    <h3 class="text-xl font-bold mb-4 text-center font-lora">Common Scene Settings</h3>
                    <div class="chart-container">
                        <canvas id="settingChart"></canvas>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md w-full lg:col-span-2">
                    <h3 class="text-xl font-bold mb-4 text-center font-lora">Prevalent Emotional Tones</h3>
                     <div class="chart-container">
                        <canvas id="emotionChart"></canvas>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <div id="scene-modal" class="modal fixed inset-0 bg-black bg-opacity-60 z-50 items-center justify-center p-4">
        <div class="modal-content bg-white w-full max-w-6xl h-[90vh] rounded-lg shadow-2xl flex flex-col overflow-hidden">
            <div class="flex justify-between items-center p-4 border-b border-slate-200">
                <h2 id="modal-title" class="text-2xl font-bold font-lora text-slate-900"></h2>
                <button id="close-modal-btn" class="text-slate-500 hover:text-slate-800 text-3xl">&times;</button>
            </div>
            <div class="flex-grow flex flex-col md:flex-row overflow-hidden">
                <div id="modal-text" class="w-full md:w-1/2 p-6 overflow-y-auto text-slate-700 leading-relaxed font-lora"></div>
                <div class="w-full md:w-1/2 bg-slate-50 p-6 overflow-y-auto border-l border-slate-200">
                    <h3 class="text-xl font-bold mb-4 font-lora text-slate-800">Scene Deconstruction</h3>
                    <div id="modal-details" class="space-y-6"></div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
