<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whispers of Intimacy: An Interactive Exploration</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="data/scenes.js"></script>
    <script src="js/script.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Lora:ital,wght@0,400;0,600;1,400&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="text-slate-800">

    <header class="bg-white shadow-md sticky top-0 z-40">
        <div class="container mx-auto px-6 py-4">
            <h1 class="text-3xl font-bold text-slate-900 font-lora">Whispers of Intimacy</h1>
            <p class="text-slate-600">An Interactive Exploration of Descriptive Scenes</p>
        </div>
    </header>

    <main class="container mx-auto px-6 py-8">
        <section id="gallery-section" class="mb-16">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-slate-900 mb-2 font-lora">Scene Gallery</h2>
                <p class="text-slate-600">This gallery presents a collection of intimate, descriptive scenes. Each card represents a unique vignette, capturing a moment of connection. Click on any card to open a detailed view, which includes the full text of the scene and a breakdown of its key characters and atmospheric elements. This structure allows you to explore the nuances of each story at your own pace.</p>
            </div>
            <div id="gallery-grid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            </div>
        </section>

        <section id="analysis-section">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-slate-900 mb-2 font-lora">Thematic Analysis</h2>
                <p class="text-slate-600">The following visualizations provide a high-level analysis of the entire collection of scenes. These charts synthesize recurring data points and themes, such as character ages, common settings, and emotional tones, to reveal underlying patterns and offer a broader perspective on the narratives. This section helps in understanding the collection not just as individual stories, but as a cohesive whole with distinct characteristics.</p>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
                <div class="bg-white p-6 rounded-lg shadow-md w-full">
                    <h3 class="text-xl font-bold mb-4 text-center font-lora">Character Age Distribution</h3>
                    <div class="chart-container">
                        <canvas id="ageChart"></canvas>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md w-full">
                    <h3 class="text-xl font-bold mb-4 text-center font-lora">Common Scene Settings</h3>
                    <div class="chart-container">
                        <canvas id="settingChart"></canvas>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md w-full lg:col-span-2">
                    <h3 class="text-xl font-bold mb-4 text-center font-lora">Prevalent Emotional Tones</h3>
                     <div class="chart-container">
                        <canvas id="emotionChart"></canvas>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <div id="scene-modal" class="modal fixed inset-0 bg-black bg-opacity-60 z-50 items-center justify-center p-4">
        <div class="modal-content bg-white w-full max-w-6xl h-[90vh] rounded-lg shadow-2xl flex flex-col overflow-hidden">
            <div class="flex justify-between items-center p-4 border-b border-slate-200">
                <h2 id="modal-title" class="text-2xl font-bold font-lora text-slate-900"></h2>
                <button id="close-modal-btn" class="text-slate-500 hover:text-slate-800 text-3xl">&times;</button>
            </div>
            <div class="flex-grow flex flex-col md:flex-row overflow-hidden">
                <div id="modal-text" class="w-full md:w-1/2 p-6 overflow-y-auto text-slate-700 leading-relaxed font-lora"></div>
                <div class="w-full md:w-1/2 bg-slate-50 p-6 overflow-y-auto border-l border-slate-200">
                    <h3 class="text-xl font-bold mb-4 font-lora text-slate-800">Scene Deconstruction</h3>
                    <div id="modal-details" class="space-y-6"></div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
