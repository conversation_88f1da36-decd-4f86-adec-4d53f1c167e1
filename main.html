<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whispers of Intimacy: An Interactive Exploration</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="data/scenes.js"></script>
    <script src="js/script.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Lora:ital,wght@0,400;0,600;1,400&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="text-primary">

    <header class="header-bg sticky top-0 z-40">
        <div class="container mx-auto px-6 py-6">
            <h1 class="text-4xl font-bold text-primary font-lora tracking-tight">Whispers of Intimacy</h1>
            <p class="text-secondary mt-2 text-lg">An Interactive Exploration of Descriptive Scenes</p>
        </div>
    </header>

    <main class="container mx-auto px-6 py-12">
        <section id="gallery-section" class="mb-20">
            <div class="mb-12">
                <h2 class="text-3xl font-bold text-primary mb-4 font-lora">Scene Gallery</h2>
                <p class="text-secondary text-lg leading-relaxed max-w-4xl mb-8">This gallery presents a collection of intimate, descriptive scenes. Each card represents a unique vignette, capturing a moment of connection. Click on any card to open a detailed view, which includes the full text of the scene and a breakdown of its key characters and atmospheric elements. This structure allows you to explore the nuances of each story at your own pace.</p>

                <!-- Advanced Search Bar -->
                <div class="search-container mb-8">
                    <div class="search-input-wrapper relative">
                        <input
                            type="text"
                            id="search-input"
                            placeholder="Search scenes by keywords, characters, settings, emotions..."
                            class="search-input w-full px-6 py-4 text-lg rounded-xl border-2 focus:outline-none focus:border-white transition-all duration-300"
                        >
                        <button id="clear-search" class="search-clear-btn absolute right-4 top-1/2 transform -translate-y-1/2 text-2xl opacity-0 transition-opacity duration-200 hover:text-white">
                            ×
                        </button>
                    </div>

                    <!-- Search Filters -->
                    <div class="search-filters mt-4 flex flex-wrap gap-3">
                        <button class="filter-btn active" data-filter="all">All</button>
                        <button class="filter-btn" data-filter="setting">Settings</button>
                        <button class="filter-btn" data-filter="emotion">Emotions</button>
                        <button class="filter-btn" data-filter="character">Characters</button>
                        <button class="filter-btn" data-filter="atmosphere">Atmosphere</button>
                    </div>

                    <!-- Search Results Info -->
                    <div id="search-results-info" class="search-results-info mt-4 text-secondary"></div>
                </div>
            </div>
            <div id="gallery-grid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            </div>
        </section>

        <section id="analysis-section">
            <div class="mb-12">
                <h2 class="text-3xl font-bold text-primary mb-4 font-lora">Thematic Analysis</h2>
                <p class="text-secondary text-lg leading-relaxed max-w-4xl">The following visualizations provide a high-level analysis of the entire collection of scenes. These charts synthesize recurring data points and themes, such as character ages, common settings, and emotional tones, to reveal underlying patterns and offer a broader perspective on the narratives. This section helps in understanding the collection not just as individual stories, but as a cohesive whole with distinct characteristics.</p>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
                <div class="chart-card p-8 rounded-xl w-full">
                    <h3 class="text-xl font-bold mb-6 text-center font-lora text-primary">Character Age Distribution</h3>
                    <div class="chart-container">
                        <canvas id="ageChart"></canvas>
                    </div>
                </div>
                <div class="chart-card p-8 rounded-xl w-full">
                    <h3 class="text-xl font-bold mb-6 text-center font-lora text-primary">Common Scene Settings</h3>
                    <div class="chart-container">
                        <canvas id="settingChart"></canvas>
                    </div>
                </div>
                <div class="chart-card p-8 rounded-xl w-full lg:col-span-2">
                    <h3 class="text-xl font-bold mb-6 text-center font-lora text-primary">Prevalent Emotional Tones</h3>
                     <div class="chart-container">
                        <canvas id="emotionChart"></canvas>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <div id="scene-modal" class="modal fixed inset-0 modal-backdrop z-50 items-center justify-center p-4">
        <div class="modal-content modal-content-bg w-full max-w-6xl h-[90vh] rounded-xl flex flex-col overflow-hidden">
            <div class="flex justify-between items-center p-6 border-b modal-divider">
                <h2 id="modal-title" class="text-2xl font-bold font-lora text-primary"></h2>
                <button id="close-modal-btn" class="btn-close text-4xl font-light">&times;</button>
            </div>
            <div class="flex-grow flex flex-col md:flex-row overflow-hidden">
                <div id="modal-text" class="w-full md:w-1/2 p-8 overflow-y-auto text-secondary leading-relaxed font-lora text-lg"></div>
                <div class="w-full md:w-1/2 modal-sidebar p-8 overflow-y-auto">
                    <h3 class="text-xl font-bold mb-6 font-lora text-primary">Scene Deconstruction</h3>
                    <div id="modal-details" class="space-y-4"></div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
